import type { ICloudflareEnv } from '@types';
import { getContext } from 'hono/context-storage';
import { OpenAI } from 'openai';
import { VoyageAIClient } from 'voyageai';
import getDB from './getDB';

/**
 * Gets the current Hono context
 * @returns Promise resolving to the current execution context
 */
export const getCtx = async () => {
  return getContext<Env>();
};

/**
 * Gets the Cloudflare Workers environment bindings
 * @returns Promise resolving to environment variables and bindings
 */
export const getEnv = async () => {
  return (await getCtx()).env;
};

/**
 * Gets the Cloudflare AI binding for AI model inference
 * @returns Promise resolving to the AI binding
 */
export const getAi = async () => {
  return (await getEnv()).ai;
};

/**
 * Gets the Cloudflare KV namespace binding for key-value storage
 * @returns Promise resolving to the KV namespace
 */
export const getKV = async () => {
  return (await getEnv()).kv;
};

/**
 * Gets the Cloudflare Vectorize binding for vector database operations
 * @returns Promise resolving to the Vectorize index
 */
export const getVDB = async () => {
  return (await getEnv()).vdb;
};

/**
 * Gets the Cloudflare R2 binding for object storage
 * @returns Promise resolving to the R2 bucket
 */
export const getR2 = async () => {
  return (await getEnv()).r2;
};

/**
 * Gets the Cloudflare Browser binding for browser automation
 * @returns Promise resolving to the browser fetcher
 */
export const getBrowser = async () => {
  return (await getEnv()).browser;
};

/**
 * Gets an OpenAI client instance with proper configuration
 * Supports both direct OpenAI API and OpenRouter proxy
 *
 * @returns Promise resolving to configured OpenAI client
 * @throws Error if no API key is configured
 *
 * @example
 * ```typescript
 * const openai = await getOpenAI();
 * const response = await openai.embeddings.create({
 *   model: 'text-embedding-3-large',
 *   input: 'Hello world'
 * });
 * ```
 */
export const getOpenAI = async () => {
  const env = await getEnv();

  // Use OpenRouter as the base URL if configured, otherwise use OpenAI directly
  const baseURL = env.OPENAI_BASE_URL || 'https://api.openai.com/v1';
  const apiKey = env.OPENAI_API_KEY || env.OPENROUTER_API_KEY;

  if (!apiKey) {
    throw new Error(
      'OpenAI API key not configured. Set OPENAI_API_KEY or OPENROUTER_API_KEY environment variable.'
    );
  }

  return new OpenAI({
    apiKey,
    baseURL,
  });
};

/**
 * Gets a VoyageAI client instance with proper configuration
 *
 * @returns Promise resolving to configured VoyageAI client
 * @throws Error if no API key is configured
 *
 * @example
 * ```typescript
 * const voyage = await getVoyageAI();
 * const response = await voyage.embed({
 *   model: 'voyage-3-large',
 *   input: 'Hello world'
 * });
 * ```
 */
export const getVoyageAI = async () => {
  const env = await getEnv();
  const apiKey = env.VOYAGEAI_API_KEY;

  if (!apiKey) {
    throw new Error('VoyageAI API key not configured. Set VOYAGEAI_API_KEY environment variable.');
  }

  return new VoyageAIClient({
    apiKey,
  });
};

/**
 * Application configuration interface
 *
 * @interface IAppConfig
 * @property environment - Current deployment environment
 * @property rateLimitEnabled - Whether rate limiting is enabled
 * @property requestsPerMinute - Maximum requests per minute per client
 * @property corsEnabled - Whether CORS is enabled
 * @property maxFileSize - Maximum file upload size in bytes
 * @property allowedFileExtensions - List of allowed file extensions
 * @property defaultEmbeddingProvider - Default embedding provider to use
 * @property defaultEmbeddingModel - Default embedding model to use
 * @property vectorDimensions - Vector dimensions for embeddings
 * @property mcpMaxConnections - Maximum MCP WebSocket connections
 * @property mcpEnabled - Whether MCP protocol is enabled
 */
export interface IAppConfig {
  // Core settings
  environment: 'development' | 'staging' | 'production';

  // Security settings
  rateLimitEnabled: boolean;
  requestsPerMinute: number;
  corsEnabled: boolean;

  // File upload settings
  maxFileSize: number;
  allowedFileExtensions: string[];

  // Embedding settings
  defaultEmbeddingProvider: 'openai' | 'voyageai' | 'cloudflare';
  defaultEmbeddingModel: string;
  vectorDimensions: number;

  // MCP settings
  mcpMaxConnections: number;
  mcpEnabled: boolean;
}

/**
 * Gets the application configuration from environment variables
 *
 * @returns Promise resolving to complete application configuration
 *
 * @example
 * ```typescript
 * const config = await getConfig();
 * console.log(`Rate limiting: ${config.rateLimitEnabled}`);
 * console.log(`Max file size: ${config.maxFileSize} bytes`);
 * console.log(`Default provider: ${config.defaultEmbeddingProvider}`);
 * ```
 */
export async function getConfig(): Promise<IAppConfig> {
  let env: Partial<ICloudflareEnv> = {};

  try {
    env = await getEnv();
  } catch (_error) {
    // In test environment or when context is not available, use defaults
    env = {};
  }

  return {
    // Determine environment (default to development for Cloudflare Workers)
    environment: 'development',

    // Security settings
    rateLimitEnabled: env.RATE_LIMIT_ENABLED !== 'false',
    requestsPerMinute: parseInt(env.RATE_LIMIT_REQUESTS_PER_MINUTE || '60'),
    corsEnabled: true,

    // File upload settings
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileExtensions: env.ALLOWED_FILE_EXTENSIONS
      ? env.ALLOWED_FILE_EXTENSIONS.split(',').map((ext) => ext.trim())
      : [
          '.txt',
          '.md',
          '.json',
          '.pdf',
          '.doc',
          '.docx',
          '.py',
          '.js',
          '.ts',
          '.jsx',
          '.tsx',
          '.java',
          '.cpp',
          '.c',
          '.h',
          '.cs',
          '.php',
          '.rb',
          '.go',
          '.rs',
          '.swift',
          '.kt',
          '.scala',
          '.r',
          '.sql',
          '.sh',
          '.bat',
          '.ps1',
          '.yaml',
          '.yml',
          '.xml',
          '.html',
          '.css',
          '.scss',
          '.less',
        ],

    // Embedding settings
    defaultEmbeddingProvider: env.EMBEDDING_PROVIDER || 'cloudflare',
    defaultEmbeddingModel: env.EMBEDDING_MODEL || '@cf/baai/bge-large-en-v1.5',
    vectorDimensions: parseInt(env.VECTOR_DIMENSIONS || env.EMBEDDING_DIMENSIONS || '1024'),

    // MCP settings
    mcpMaxConnections: parseInt(env.MCP_MAX_CONNECTIONS || '100'),
    mcpEnabled: true,
  };
}

// Export services for backward compatibility
export {
  ChunkingService,
  CodeDetectionService,
  chunkingService,
  codeDetectionService,
  EmbeddingService,
  embeddingService,
} from '@services';
// Export types
export type {
  IBaseChunkMetadata,
  IChunkingConfig,
  ICodeDetectionResult,
  IEmbeddingConfig,
  IEmbeddingResult,
  ITextChunk,
  TEmbeddingProvider,
} from '@types';

// Export document configuration utilities
export * from './document-config';

export { getDB };
